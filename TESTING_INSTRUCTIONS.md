# Room Code Search and Caching - Testing Instructions

## 🧪 Complete Testing Workflow

Follow these steps to test the room code search and caching system:

### **Step 1: Prepare Test Files**

1. **CSV File**: Use `sample_rooms.csv` (already contains room codes like "01.E.24", "01.A.15", etc.)
2. **PDF File**: Use `test_document.pdf` (created with matching room codes)

### **Step 2: Start the Application**

```bash
npm run dev
```

Open browser to `http://localhost:5174/`

### **Step 3: Upload Files**

1. **Upload CSV first**:
   - Click "Upload CSV" button
   - Select `sample_rooms.csv`
   - ✅ Verify: Room codes are extracted and displayed in console

2. **Upload PDF**:
   - Click "Upload PDF" button  
   - Select `test_document.pdf`
   - ✅ Verify: PDF loads and displays

### **Step 4: Automatic Room Code Search**

After both files are uploaded:

1. **Search Status Component appears** (fixed overlay at top)
2. **Automatic search starts** within 1 second
3. **Progress bar shows** search progress across PDF pages
4. **Console logs show**:
   ```
   Extracted X room codes from Y room names:
     01.E.24 -> CHEMICAL WASTE STORAGE 01.E.24
     01.A.15 -> OFFICE 01.A.15
     ...
   
   Found room code "01.E.24" at PDF coordinates (450, 200) -> Canvas coordinates (937.50, 416.67)
   ```

### **Step 5: Verify Search Results**

When search completes:

1. **Statistics Display**:
   - Total Room Codes: X
   - Found: Y  
   - Not Found: Z
   - Success Rate: Y%

2. **Found Room Codes Section**:
   - Clickable buttons for each found room code
   - Hover shows coordinates and room name
   - Green styling indicates successful finds

### **Step 6: Test Coordinate Conversion**

Open browser console and run:

```javascript
// Check cached coordinates
console.log('Room code cache:', window.roomCodeCache)

// Verify coordinate conversion
const testCode = "01.E.24"
const codeData = window.getRoomCodeCoordinates?.(testCode)
if (codeData) {
  console.log(`${testCode} coordinates:`, codeData.coordinates)
  console.log(`Original PDF coordinates:`, codeData.pdfCoordinates)
}
```

### **Step 7: Manual Testing**

1. **Stop/Start Search**:
   - Click "Stop" during search
   - Click "Search" to restart
   - Click "Reset" to clear results

2. **Different CSV Files**:
   - Try `locations (4).csv` 
   - Verify different room codes are extracted

3. **Multiple PDFs**:
   - Upload additional PDF files
   - Verify search works across all PDFs

## 🔍 Expected Results

### **Room Code Extraction**
From `sample_rooms.csv`, should extract codes like:
- `01.E.28` from "BIOWASTE 01.E.28"
- `01.E.24` from "CHEMICAL WASTE STORAGE 01.E.24"
- `01.A.15` from "OFFICE 01.A.15"
- `01.A.20` from "CONFERENCE ROOM 01.A.20"
- `01.B.05` from "LABORATORY 01.B.05"

### **PDF Search Results**
From `test_document.pdf`, should find:
- `01.E.28` at coordinates ~(937, 417)
- `01.A.15` at coordinates ~(937, 458) 
- `01.B.05` at coordinates ~(937, 542)
- `02.A.08` at coordinates ~(937, 375)
- `02.B.03` at coordinates ~(937, 417)

### **Coordinate Conversion**
- PDF coordinates (450, 200) → Canvas coordinates (~937, 417)
- Scale factor: ~2.083 (150 DPI / 72 DPI)
- Width/height scaled proportionally

## 🐛 Troubleshooting

### **No Room Codes Extracted**
- Check CSV format (room names in last column)
- Verify room names end with pattern "XX.Y.ZZ"
- Check console for extraction logs

### **Search Not Starting**
- Ensure both CSV and PDF are uploaded
- Check for JavaScript errors in console
- Verify PDF.js worker is loading

### **No Room Codes Found**
- Check if PDF contains the exact room code text
- Verify PDF text extraction is working
- Try different PDF files

### **Coordinate Issues**
- Check PDF dimensions vs canvas dimensions
- Verify scale factor calculations
- Test with known coordinates

## 📊 Performance Benchmarks

Expected performance:
- **Room Code Extraction**: < 100ms for 100 room names
- **PDF Text Extraction**: ~500ms per page
- **Search Speed**: ~1-2 seconds per PDF page
- **Memory Usage**: Minimal (coordinates cached efficiently)

## ✅ Success Criteria

The system is working correctly if:

1. ✅ Room codes are extracted from CSV room names
2. ✅ PDF text content is searchable
3. ✅ Room codes are found in PDF text
4. ✅ Coordinates are converted from PDF to canvas system
5. ✅ Results are cached for quick access
6. ✅ UI provides clear feedback and controls
7. ✅ System handles multiple PDFs and large CSV files
8. ✅ Coordinate conversion is accurate and reversible

## 🚀 Next Steps

After successful testing:
1. Integrate with annotation creation system
2. Add export functionality for found coordinates
3. Implement smart annotation placement
4. Add batch annotation creation for all found codes
5. Create coordinate validation and bounds checking
