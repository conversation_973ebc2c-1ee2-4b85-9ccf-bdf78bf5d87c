// Test script for distance-based room sorting functionality
// This can be run in the browser console to verify the implementation

// Mock data for testing
const mockAnnotation = {
  type: 'rectangle',
  x: 500,
  y: 300,
  width: 100,
  height: 50
}

const mockPolygonAnnotation = {
  type: 'polygon',
  points: [
    { x: 400, y: 250 },
    { x: 600, y: 250 },
    { x: 600, y: 350 },
    { x: 400, y: 350 }
  ]
}

const mockRoomCodeCache = new Map([
  ['01.E.24', {
    code: '01.E.24',
    roomName: 'CHEMICAL WASTE STORAGE 01.E.24',
    coordinates: { x: 520, y: 320 }, // Very close to annotation center (550, 325)
    found: true
  }],
  ['01.A.15', {
    code: '01.A.15',
    roomName: 'OFFICE 01.A.15',
    coordinates: { x: 600, y: 400 }, // Medium distance
    found: true
  }],
  ['01.B.05', {
    code: '01.B.05',
    roomName: 'LABORATORY 01.B.05',
    coordinates: { x: 800, y: 600 }, // Far distance
    found: true
  }],
  ['02.A.08', {
    code: '02.A.08',
    roomName: 'MEETING ROOM 02.A.08',
    coordinates: { x: 545, y: 315 }, // Very close to annotation center
    found: true
  }]
])

const mockRoomNames = [
  'CHEMICAL WASTE STORAGE 01.E.24',
  'OFFICE 01.A.15',
  'LABORATORY 01.B.05',
  'MEETING ROOM 02.A.08',
  'STORAGE ROOM 01.C.10', // No coordinates available
  'BREAK ROOM 03.B.15'    // No coordinates available
]

// Mock extractRoomCode function
const mockExtractRoomCode = (roomName) => {
  const match = roomName.match(/([0-9]{1,3}\.[A-Z]{1,2}\.[0-9]{1,3})$/)
  return match ? match[1] : null
}

// Import the distance calculation functions (these would be available in the browser)
const calculateAnnotationCentroid = (annotation) => {
  if (!annotation) return { x: 0, y: 0 }

  if (annotation.type === 'rectangle') {
    return {
      x: annotation.x + annotation.width / 2,
      y: annotation.y + annotation.height / 2
    }
  } else if (annotation.type === 'polygon' && annotation.points && annotation.points.length > 0) {
    const sumX = annotation.points.reduce((sum, point) => sum + point.x, 0)
    const sumY = annotation.points.reduce((sum, point) => sum + point.y, 0)
    return {
      x: sumX / annotation.points.length,
      y: sumY / annotation.points.length
    }
  }

  return { x: 0, y: 0 }
}

const calculateDistance = (point1, point2) => {
  if (!point1 || !point2) return Infinity
  
  const dx = point1.x - point2.x
  const dy = point1.y - point2.y
  return Math.sqrt(dx * dx + dy * dy)
}

const sortRoomNamesByDistance = (roomNames, annotation, roomCodeCache, extractRoomCode) => {
  if (!roomNames || !annotation || !roomCodeCache || !extractRoomCode) {
    return roomNames.map(roomName => ({
      roomName,
      distance: Infinity,
      hasCoordinates: false,
      coordinates: null
    }))
  }

  const annotationCentroid = calculateAnnotationCentroid(annotation)
  
  const roomsWithDistance = roomNames.map(roomName => {
    const roomCode = extractRoomCode(roomName)
    
    if (roomCode && roomCodeCache.has(roomCode)) {
      const codeData = roomCodeCache.get(roomCode)
      const roomCoordinates = codeData.coordinates
      const distance = calculateDistance(annotationCentroid, roomCoordinates)
      
      return {
        roomName,
        distance,
        hasCoordinates: true,
        coordinates: roomCoordinates,
        roomCode,
        codeData
      }
    } else {
      return {
        roomName,
        distance: Infinity,
        hasCoordinates: false,
        coordinates: null,
        roomCode
      }
    }
  })

  return roomsWithDistance.sort((a, b) => {
    if (a.hasCoordinates && !b.hasCoordinates) return -1
    if (!a.hasCoordinates && b.hasCoordinates) return 1
    
    if (a.hasCoordinates && b.hasCoordinates) {
      if (a.distance !== b.distance) {
        return a.distance - b.distance
      }
    }
    
    return a.roomName.localeCompare(b.roomName)
  })
}

const formatDistance = (distance) => {
  if (distance === Infinity || isNaN(distance)) {
    return 'No location'
  }
  
  if (distance < 1) {
    return '< 1px'
  } else if (distance < 100) {
    return `${Math.round(distance)}px`
  } else {
    return `${Math.round(distance / 10) * 10}px`
  }
}

// Run tests
console.log('Testing Distance-Based Room Sorting')
console.log('=' .repeat(50))

// Test 1: Rectangle annotation centroid calculation
const rectCentroid = calculateAnnotationCentroid(mockAnnotation)
console.log(`Rectangle annotation centroid: (${rectCentroid.x}, ${rectCentroid.y})`)
console.log(`Expected: (550, 325)`)
console.log(`✅ ${rectCentroid.x === 550 && rectCentroid.y === 325 ? 'PASS' : 'FAIL'}`)
console.log('')

// Test 2: Polygon annotation centroid calculation
const polygonCentroid = calculateAnnotationCentroid(mockPolygonAnnotation)
console.log(`Polygon annotation centroid: (${polygonCentroid.x}, ${polygonCentroid.y})`)
console.log(`Expected: (500, 300)`)
console.log(`✅ ${polygonCentroid.x === 500 && polygonCentroid.y === 300 ? 'PASS' : 'FAIL'}`)
console.log('')

// Test 3: Distance calculation
const testDistance = calculateDistance({ x: 0, y: 0 }, { x: 3, y: 4 })
console.log(`Distance from (0,0) to (3,4): ${testDistance}`)
console.log(`Expected: 5`)
console.log(`✅ ${testDistance === 5 ? 'PASS' : 'FAIL'}`)
console.log('')

// Test 4: Room sorting by distance
const sortedRooms = sortRoomNamesByDistance(mockRoomNames, mockAnnotation, mockRoomCodeCache, mockExtractRoomCode)

console.log('Sorted rooms by distance from rectangle annotation:')
console.log('-' .repeat(50))
sortedRooms.forEach((room, index) => {
  console.log(`${index + 1}. ${room.roomName}`)
  console.log(`   Distance: ${formatDistance(room.distance)}`)
  console.log(`   Has coordinates: ${room.hasCoordinates}`)
  if (room.hasCoordinates) {
    console.log(`   Coordinates: (${room.coordinates.x}, ${room.coordinates.y})`)
  }
  console.log('')
})

// Test 5: Verify sorting order
const roomsWithCoords = sortedRooms.filter(room => room.hasCoordinates)
const isCorrectOrder = roomsWithCoords.every((room, index) => {
  if (index === 0) return true
  return room.distance >= roomsWithCoords[index - 1].distance
})

console.log(`Sorting order verification: ${isCorrectOrder ? '✅ PASS' : '❌ FAIL'}`)
console.log('')

// Test 6: Rooms without coordinates should come after rooms with coordinates
const roomsWithoutCoords = sortedRooms.filter(room => !room.hasCoordinates)
const lastRoomWithCoords = roomsWithCoords[roomsWithCoords.length - 1]
const firstRoomWithoutCoords = roomsWithoutCoords[0]

if (roomsWithCoords.length > 0 && roomsWithoutCoords.length > 0) {
  const correctPlacement = sortedRooms.indexOf(lastRoomWithCoords) < sortedRooms.indexOf(firstRoomWithoutCoords)
  console.log(`Rooms without coordinates placement: ${correctPlacement ? '✅ PASS' : '❌ FAIL'}`)
} else {
  console.log('Rooms without coordinates placement: ⚠️ SKIP (no rooms without coordinates)')
}

console.log('')
console.log('=' .repeat(50))
console.log('Distance-based room sorting test completed!')

// Expected order based on distances:
// 1. MEETING ROOM 02.A.08 (distance ~30px)
// 2. CHEMICAL WASTE STORAGE 01.E.24 (distance ~32px)  
// 3. OFFICE 01.A.15 (distance ~90px)
// 4. LABORATORY 01.B.05 (distance ~360px)
// 5. BREAK ROOM 03.B.15 (no coordinates)
// 6. STORAGE ROOM 01.C.10 (no coordinates)
