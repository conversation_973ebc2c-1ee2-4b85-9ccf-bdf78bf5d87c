import React, { useState, useEffect, useRef } from 'react'

const Toolbar = ({
  fileInputRef,
  onFileUpload,
  csvInputRef,
  onCSVUpload,
  csvFileName,
  roomNames,
  allPdfData,
  currentPdfIndex,
  onSwitchToPdf,
  pdfPages,
  currentPageIndex,
  onSetCurrentPageIndex,
  zoom,
  onSetZoom,
  onAutoFitToScreen,
  onResetCanvasPosition,
  drawingMode,
  onSetDrawingMode,
  currentAnnotation,
  onFinishPolygon,
  rectangleStartPoint,
  onSetRectangleStartPoint,
  onExportAnnotations,
  onExportAnnotatedPDF,
  getCurrentAnnotations,
  selectedAnnotations,
  onCopyAnnotation,
  onDeleteAnnotation,
  showRoomDropdownForAnnotation,
  // Hierarchical filter props
  csvStructure,
  hierarchicalFilter,
  onShowFilterModal,
  // Distance sorting props
  useDistanceSorting,
  onToggleDistanceSorting,
  roomCodeCache
}) => {
  const [showPdfDropdown, setShowPdfDropdown] = useState(false)
  const pdfDropdownRef = useRef(null)

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (pdfDropdownRef.current && !pdfDropdownRef.current.contains(event.target)) {
        setShowPdfDropdown(false)
      }
    }

    if (showPdfDropdown) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [showPdfDropdown])

  return (
    <div className="toolbar">
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf"
        multiple
        onChange={onFileUpload}
        style={{ display: 'none' }}
      />

      <input
        ref={csvInputRef}
        type="file"
        accept=".csv"
        onChange={onCSVUpload}
        style={{ display: 'none' }}
      />

      <div className="toolbar-section">
        <div className="csv-upload-section">
          {!csvFileName && (
            <button
              className="icon-button"
              onClick={() => csvInputRef.current?.click()}
              title="Upload CSV with room names"
            >
              📊
            </button>
          )}
          {csvFileName && (
            <span className="csv-status loaded" title={`${roomNames.length} room names loaded from ${csvFileName}`}>
              {roomNames.length} rooms
            </span>
          )}
        </div>

        {/* Hierarchical Filter Control */}
        {csvStructure && csvStructure.maxDepth > 1 && (
          <div className="filter-control-section">
            <button
              className={`icon-button ${hierarchicalFilter?.isFilterActive ? 'active' : ''}`}
              onClick={onShowFilterModal}
              title={hierarchicalFilter?.isFilterActive ?
                `Filter active: ${hierarchicalFilter.selectedPath.join(' → ')}` :
                'Set room filter'
              }
            >
              🔍
            </button>
            {hierarchicalFilter?.isFilterActive && (
              <div className="filter-status">
                <span className="filter-path" title={hierarchicalFilter.selectedPath.join(' → ')}>
                  {hierarchicalFilter.selectedPath.slice(-2).join(' → ')}
                </span>
                <button
                  className="clear-filter-btn"
                  onClick={() => hierarchicalFilter.clearFilters()}
                  title="Clear filter"
                >
                  ✕
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {allPdfData.current.length > 0 && (
        <>
          {allPdfData.current.length > 1 && (
            <div className="toolbar-section">
              <div className="pdf-dropdown-container" ref={pdfDropdownRef}>
                <button
                  className="icon-button"
                  onClick={() => setShowPdfDropdown(!showPdfDropdown)}
                  title="Select PDF"
                >
                  📋
                </button>
                {showPdfDropdown && (
                  <div className="pdf-dropdown">
                    {allPdfData.current.map((pdfData, index) => (
                      <div
                        key={index}
                        className={`pdf-dropdown-item ${index === currentPdfIndex ? 'active' : ''}`}
                        onClick={() => {
                          onSwitchToPdf(index)
                          setShowPdfDropdown(false)
                        }}
                      >
                        {pdfData.name}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <span>{currentPdfIndex + 1}/{allPdfData.current.length}</span>
            </div>
          )}

          {pdfPages.length > 1 && (
            <div className="toolbar-section">
              <button
                className="icon-button"
                onClick={() => onSetCurrentPageIndex(Math.max(0, currentPageIndex - 1))}
                disabled={currentPageIndex === 0}
                title="Previous Page"
              >
                ◀
              </button>
              <span>Page {currentPageIndex + 1}/{pdfPages.length}</span>
              <button
                className="icon-button"
                onClick={() => onSetCurrentPageIndex(Math.min(pdfPages.length - 1, currentPageIndex + 1))}
                disabled={currentPageIndex === pdfPages.length - 1}
                title="Next Page"
              >
                ▶
              </button>
            </div>
          )}

          <div className="toolbar-section">
            <button
              className="icon-button"
              onClick={() => onSetZoom(Math.max(0.1, zoom - 0.1))}
              disabled={zoom <= 0.1}
              title="Zoom Out (Mouse wheel)"
            >
              🔍-
            </button>
            <button
              className="icon-button"
              onClick={onAutoFitToScreen}
              title="Fit to Screen"
            >
              📐
            </button>
            <button
              className="icon-button"
              onClick={() => onSetZoom(1)}
              title="Reset to 100%"
            >
              1:1
            </button>
            <button
              className="icon-button"
              onClick={onResetCanvasPosition}
              title="Reset Position"
            >
              🎯
            </button>
            <span>{Math.round(zoom * 100)}%</span>
            <button
              className="icon-button"
              onClick={() => onSetZoom(Math.min(15, zoom + 0.1))}
              disabled={zoom >= 15}
              title="Zoom In (Mouse wheel)"
            >
              🔍+
            </button>
          </div>

          <div className="toolbar-section">
            <button
              className={`icon-button ${drawingMode === 'hand' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('hand')}
              title="Hand Tool (drag to pan)"
            >
              ✋
            </button>
            <button
              className={`icon-button ${drawingMode === 'select' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('select')}
              title="Select Tool (click and drag annotations)"
            >
              🔲
            </button>
            <button
              className={`icon-button ${drawingMode === 'rectangle' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('rectangle')}
              title="Rectangle Tool (click and drag)"
            >
              ⬜
            </button>
            <button
              className={`icon-button ${drawingMode === 'polygon' ? 'active' : ''}`}
              onClick={() => onSetDrawingMode('polygon')}
              title="Polygon Tool (multi-click)"
            >
              🔺
            </button>
            {currentAnnotation && drawingMode === 'polygon' && (
              <button
                className="icon-button"
                onClick={(event) => {
                  if (roomNames.length > 0 && showRoomDropdownForAnnotation) {
                    // Use room dropdown for polygon
                    onFinishPolygon((annotation) => {
                      showRoomDropdownForAnnotation(annotation, event)
                    })
                  } else {
                    // No CSV loaded, finish normally
                    onFinishPolygon()
                  }
                }}
                title="Finish Polygon (Enter)"
              >
                ✓
              </button>
            )}
            {rectangleStartPoint && drawingMode === 'rectangle' && (
              <button
                className="icon-button"
                onClick={() => onSetRectangleStartPoint(null)}
                title="Cancel Rectangle (Esc)"
              >
                ✕
              </button>
            )}
          </div>

          {/* Distance sorting toggle - only show if room codes are available */}
          {roomCodeCache && roomCodeCache.size > 0 && (
            <div className="toolbar-section">
              <button
                className={`icon-button ${useDistanceSorting ? 'active' : ''}`}
                onClick={onToggleDistanceSorting}
                title={`${useDistanceSorting ? 'Disable' : 'Enable'} distance-based room sorting`}
              >
                📏
              </button>
            </div>
          )}

          <div className="toolbar-section">
            <button
              className="icon-button"
              onClick={onExportAnnotations}
              disabled={Object.keys(getCurrentAnnotations()).length === 0}
              title="Export JSON (Ctrl+S)"
            >
              💾
            </button>
            <button
              className="icon-button"
              onClick={onExportAnnotatedPDF}
              disabled={getCurrentAnnotations().length === 0 || allPdfData.current.length === 0}
              title="Export Annotated PDF"
            >
              📄✏️
            </button>
            {selectedAnnotations && selectedAnnotations.length > 0 && (
              <div className="selected-annotation-info">
                <span>
                  {selectedAnnotations.length === 1
                    ? selectedAnnotations[0].type
                    : `${selectedAnnotations.length} selected`
                  }
                </span>
                <button
                  className="icon-button"
                  onClick={() => onCopyAnnotation()}
                  title="Copy (Ctrl+C)"
                >
                  📋
                </button>
                <button
                  className="icon-button"
                  onClick={() => onDeleteAnnotation()}
                  title="Delete (Del)"
                >
                  🗑️
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default Toolbar
