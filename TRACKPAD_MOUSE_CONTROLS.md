# Trackpad and Mouse Controls for PDF Viewer

This document describes the enhanced trackpad and mouse controls implemented for the PDF annotation tool, providing a Chrome PDF viewer-like experience.

## 🖱️ Mouse Controls

### Zooming
- **Ctrl + Scroll Up/Down**: Zoom in/out at mouse cursor position
- **Cmd + Scroll Up/Down** (Mac): Zoom in/out at mouse cursor position

### Panning
- **Scroll Up/Down**: Pan PDF vertically
- **Shift + Scroll Up/Down**: Pan PDF horizontally

## 🖲️ Trackpad Controls

### Panning
- **Two-finger scroll in any direction**: Pan PDF in that direction
  - Up/Down: Vertical panning
  - Left/Right: Horizontal panning
  - Diagonal: Combined panning

### Zooming
- **Pinch in/out**: Zoom out/in at the center of the pinch gesture
- Maintains the focal point during zoom (similar to Chrome PDF viewer)

## 🎯 Features

### Smart Gesture Detection
- Automatically detects trackpad vs mouse wheel input
- Trackpad gestures have fine granularity and deltaX values
- Mouse wheel events are discrete and typically only have deltaY
- Multiple pinch detection methods for cross-browser compatibility:
  - Ctrl+wheel events (most browsers)
  - Safari/WebKit gesture events
  - Touch events for mobile devices

### Zoom Behavior
- **Zoom Range**: 10% to 1500%
- **Zoom Indicator**: Shows current zoom level briefly after zooming
- **Focal Point Zoom**: Zoom centers on mouse cursor or pinch center
- **Smooth Transitions**: 0.1s ease-out transitions for better UX

### Pan Behavior
- **Unlimited Panning**: No boundaries, can pan in any direction
- **Smooth Movement**: Direct 1:1 mapping for trackpad, 2x sensitivity for mouse
- **Multi-directional**: Supports diagonal panning on trackpad

## 🔧 Technical Implementation

### Event Handling
- Uses `wheel` events for both mouse and trackpad
- Adds `touchstart`, `touchmove`, `touchend` for pinch gestures
- Prevents default browser behaviors with `touch-action: none`

### Performance Optimizations
- Uses refs to avoid stale closures in event handlers
- Debounced zoom indicator display
- Efficient coordinate calculations

### Browser Compatibility
- Works on all modern browsers
- Optimized for both desktop and touch devices
- Handles both Ctrl (Windows/Linux) and Cmd (Mac) modifiers

## 🚀 Usage Examples

### Basic Navigation
1. **Load a PDF** in the annotation tool
2. **Use trackpad two-finger scroll** to pan around the document
3. **Pinch to zoom** in/out on specific areas
4. **Use mouse wheel with Ctrl** for precise zooming

### Advanced Navigation
1. **Shift + mouse wheel** for horizontal scrolling with mouse
2. **Regular mouse wheel** for vertical scrolling
3. **Combine with hand tool** for click-and-drag panning

## 🎨 Visual Feedback

### Zoom Indicator
- Appears in bottom-right corner during zoom operations
- Shows percentage (e.g., "150%")
- Auto-hides after 1 second
- Styled with semi-transparent background

### Cursor States
- **Crosshair**: Default annotation mode
- **Grab/Grabbing**: Hand tool mode
- **Pointer**: Select mode

## 🔍 Testing

To test the new controls:

1. **Open the PDF annotation tool**
2. **Load any PDF document**
3. **Try trackpad gestures**:
   - Two-finger scroll in different directions
   - Pinch in and out
4. **Try mouse controls**:
   - Ctrl+scroll for zoom
   - Shift+scroll for horizontal pan
   - Regular scroll for vertical pan

## 📱 Mobile Support

The implementation also supports touch devices:
- **Two-finger pinch**: Zoom in/out
- **Single finger drag**: Pan (when in hand mode)
- **Touch-optimized**: Prevents default touch behaviors

## 🛠️ Configuration

### Sensitivity Settings
- **Trackpad pan sensitivity**: 1.0x (direct mapping)
- **Mouse pan sensitivity**: 2.0x (faster movement)
- **Zoom sensitivity**: 0.1 per wheel step

### Zoom Limits
- **Minimum zoom**: 10% (0.1x)
- **Maximum zoom**: 1500% (15x)

These settings provide a balance between precision and usability across different input devices.

## 🔧 Troubleshooting

### Trackpad Pinch Not Working?

If trackpad pinch-to-zoom isn't working, try these solutions:

1. **Check Browser Settings**:
   - Ensure "Use system zoom" is enabled in browser settings
   - Disable browser's built-in pinch-to-zoom if it conflicts

2. **Try Different Approaches**:
   - Use Ctrl+scroll as an alternative to pinch
   - Check if your trackpad drivers are up to date

3. **Browser-Specific Issues**:
   - **Chrome/Edge**: Should work with Ctrl+wheel detection
   - **Firefox**: May require enabling touch events in about:config
   - **Safari**: Uses gesture events (should work automatically)

4. **System Settings**:
   - On macOS: Check System Preferences > Trackpad > Zoom
   - On Windows: Check Settings > Devices > Touchpad

### Debug Mode
To debug gesture detection, open browser console and look for:
- Wheel events with `ctrlKey: true` for pinch gestures
- `gesturestart/gesturechange/gestureend` events on Safari
- Touch events on mobile devices
