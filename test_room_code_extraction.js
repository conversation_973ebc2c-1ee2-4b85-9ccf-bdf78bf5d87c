// Simple test script to verify room code extraction
// This can be run in the browser console

// Import the extractRoomCode function (this would be available in the browser)
// For testing, we'll define it here
const extractRoomCode = (roomName) => {
  if (!roomName || typeof roomName !== 'string') return null
  
  const trimmed = roomName.trim()
  const lastSpaceIndex = trimmed.lastIndexOf(' ')
  
  if (lastSpaceIndex === -1) return null
  
  const potentialCode = trimmed.substring(lastSpaceIndex + 1)
  
  // Basic validation: room code should match pattern like "01.A.15" or "02.B.03"
  // Should have format: digits.letter.digits (with possible variations)
  if (potentialCode.includes('.') &&
      /^[0-9]{1,3}\.[A-Z]{1,2}\.[0-9]{1,3}$/i.test(potentialCode)) {
    return potentialCode
  }
  
  return null
}

// Test cases from the sample CSV
const testCases = [
  "BIOWASTE 01.E.28",
  "CHEMICAL WASTE STORAGE 01.E.24", 
  "OFFICE 01.A.15",
  "CONFERENCE ROOM 01.A.20",
  "LABORATORY 01.B.05",
  "STORAGE 01.B.12",
  "MEETING ROOM 02.A.08",
  "RESEARCH LAB 02.B.03",
  "CLEAN ROOM 02.C.01",
  "EQUIPMENT ROOM 02.C.07",
  "EXECUTIVE OFFICE 03.A.01",
  "RECEPTION 03.A.02",
  "TRAINING ROOM 03.B.10",
  "BREAK ROOM 03.B.15",
  // Edge cases
  "ROOM WITHOUT CODE",
  "ROOM WITH INVALID CODE ABC",
  "ROOM WITH NUMBER 123",
  "ROOM WITH DOTS BUT NO PATTERN A.B.C.D.E",
  "VALID ROOM 04.Z.99"
]

console.log("Testing Room Code Extraction:")
console.log("=" .repeat(50))

testCases.forEach(roomName => {
  const code = extractRoomCode(roomName)
  console.log(`"${roomName}" -> ${code || 'null'}`)
})

console.log("\n" + "=" .repeat(50))

// Expected results
const expectedResults = [
  "01.E.28", "01.E.24", "01.A.15", "01.A.20", "01.B.05", "01.B.12",
  "02.A.08", "02.B.03", "02.C.01", "02.C.07", "03.A.01", "03.A.02",
  "03.B.10", "03.B.15", null, null, null, null, "04.Z.99"
]

let passed = 0
let failed = 0

testCases.forEach((roomName, index) => {
  const result = extractRoomCode(roomName)
  const expected = expectedResults[index]
  
  if (result === expected) {
    passed++
  } else {
    failed++
    console.error(`FAIL: "${roomName}" expected "${expected}" but got "${result}"`)
  }
})

console.log(`\nTest Results: ${passed} passed, ${failed} failed`)

if (failed === 0) {
  console.log("✅ All tests passed!")
} else {
  console.log("❌ Some tests failed!")
}
