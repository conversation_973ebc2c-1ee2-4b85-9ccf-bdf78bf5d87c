import { useState, useRef, useCallback, useEffect } from 'react'

export const useCanvasHandler = (pdfPages, currentPageIndex) => {
  const [zoom, setZoom] = useState(1)
  const [canvasOffset, setCanvasOffset] = useState({ x: 0, y: 0 })
  const [isPanning, setIsPanning] = useState(false)
  const [panStart, setPanStart] = useState({ x: 0, y: 0 })
  const [showZoomIndicator, setShowZoomIndicator] = useState(false)

  const canvasRef = useRef(null)
  const baseImages = useRef({})

  // Refs to store current values for zoom calculations
  const zoomRef = useRef(zoom)
  const canvasOffsetRef = useRef(canvasOffset)

  // Touch gesture state for pinch-to-zoom
  const [touchState, setTouchState] = useState({
    initialDistance: 0,
    initialZoom: 1,
    initialCenter: { x: 0, y: 0 },
    isGesturing: false
  })
  const touchStateRef = useRef(touchState)

  // Update refs when state changes
  useEffect(() => {
    zoomRef.current = zoom
  }, [zoom])

  useEffect(() => {
    canvasOffsetRef.current = canvasOffset
  }, [canvasOffset])

  useEffect(() => {
    touchStateRef.current = touchState
  }, [touchState])

  // Convert screen coordinates to canvas coordinates
  const screenToCanvasCoordinates = useCallback((x, y) => {
    const canvas = canvasRef.current
    if (!canvas) {
      console.log('Canvas ref is null!')
      return { x: 0, y: 0 }
    }

    const rect = canvas.getBoundingClientRect()


    // Simple coordinate conversion for now
    const canvasX = ((x - rect.left) / rect.width) * canvas.width
    const canvasY = ((y - rect.top) / rect.height) * canvas.height

    // Ensure coordinates are within canvas bounds
    const boundedX = Math.max(0, Math.min(canvas.width, canvasX))
    const boundedY = Math.max(0, Math.min(canvas.height, canvasY))



    return {
      x: boundedX,
      y: boundedY
    }
  }, [zoom, canvasOffset])

  // Calculate optimal zoom for screen size
  const calculateOptimalZoom = useCallback(() => {
    if (pdfPages.length === 0) return 1

    const currentPage = pdfPages[currentPageIndex]
    if (!currentPage) return 1

    // Get available space (accounting for toolbar and potential sidebar)
    const availableWidth = window.innerWidth - 100 // Some padding
    const availableHeight = window.innerHeight - 200 // Account for toolbar

    const scaleX = availableWidth / currentPage.width
    const scaleY = availableHeight / currentPage.height

    // Use the smaller scale to ensure the PDF fits in the viewport
    const optimalZoom = Math.min(scaleX, scaleY, 1) // Don't zoom in beyond 100%



    return Math.max(0.2, optimalZoom) // Minimum zoom of 20%
  }, [pdfPages, currentPageIndex])

  // Auto-fit PDF to screen when switching PDFs or pages
  const autoFitToScreen = useCallback(() => {
    const optimalZoom = calculateOptimalZoom()
    setZoom(optimalZoom)
    setCanvasOffset({ x: 0, y: 0 }) // Reset pan when auto-fitting
  }, [calculateOptimalZoom])

  // Reset canvas position to center
  const resetCanvasPosition = useCallback(() => {
    setCanvasOffset({ x: 0, y: 0 })
  }, [])

  // Handle mouse wheel and trackpad gestures
  const handleWheel = useCallback((event) => {
    try {
      event.preventDefault()
      event.stopPropagation()

      const canvas = canvasRef.current
      if (!canvas) return

      // Get mouse position relative to the canvas
      const rect = canvas.getBoundingClientRect()
      const mouseX = event.clientX - rect.left
      const mouseY = event.clientY - rect.top

      // Get current values from refs to avoid stale closures
      const currentZoom = zoomRef.current
      const currentOffset = canvasOffsetRef.current

      // Enhanced trackpad detection
      const isTrackpad = Math.abs(event.deltaX) > 0 || (Math.abs(event.deltaY) < 50 && event.deltaY % 1 !== 0)

      // Detect pinch gestures - different browsers handle this differently:
      // 1. Most browsers: Ctrl+wheel for trackpad pinch
      // 2. Some browsers: wheel events with deltaZ
      // 3. Safari: wheel events with specific deltaY patterns
      const isPinchGesture = (event.ctrlKey && isTrackpad) ||
                            (event.deltaZ && Math.abs(event.deltaZ) > 0) ||
                            (isTrackpad && Math.abs(event.deltaY) > 0 && event.deltaMode === 0)

      if (isPinchGesture) {
        // Trackpad pinch-to-zoom behavior
        console.log('Pinch gesture detected:', { ctrlKey: event.ctrlKey, deltaY: event.deltaY, deltaZ: event.deltaZ })
        const zoomFactor = 0.02 // More sensitive for trackpad pinch
        const delta = event.deltaY > 0 ? -zoomFactor : zoomFactor
        const newZoom = Math.max(0.1, Math.min(15, currentZoom + delta))

        if (newZoom !== currentZoom) {
          const zoomRatio = newZoom / currentZoom

          // Calculate the point that should remain fixed (relative to canvas center)
          const canvasCenterX = rect.width / 2
          const canvasCenterY = rect.height / 2

          // Distance from mouse to canvas center
          const deltaX = mouseX - canvasCenterX
          const deltaY = mouseY - canvasCenterY

          // Calculate new offset to keep the mouse point fixed
          const newOffset = {
            x: currentOffset.x - deltaX * (zoomRatio - 1),
            y: currentOffset.y - deltaY * (zoomRatio - 1)
          }

          setZoom(newZoom)
          setCanvasOffset(newOffset)

          // Show zoom indicator briefly
          setShowZoomIndicator(true)
          setTimeout(() => setShowZoomIndicator(false), 1000)
        }
      } else if (isTrackpad && !event.ctrlKey) {
        // Trackpad behavior: two-finger scroll for panning
        const panSensitivity = 1.0
        const newOffset = {
          x: currentOffset.x - event.deltaX * panSensitivity,
          y: currentOffset.y - event.deltaY * panSensitivity
        }
        setCanvasOffset(newOffset)
      } else {
        // Mouse wheel behavior
        if (event.ctrlKey || event.metaKey) {
          // Ctrl/Cmd + scroll = zoom (like Chrome PDF viewer)
          const zoomFactor = 0.1
          const delta = event.deltaY > 0 ? -zoomFactor : zoomFactor
          const newZoom = Math.max(0.1, Math.min(15, currentZoom + delta))

          if (newZoom !== currentZoom) {
            const zoomRatio = newZoom / currentZoom

            // Calculate the point that should remain fixed (relative to canvas center)
            const canvasCenterX = rect.width / 2
            const canvasCenterY = rect.height / 2

            // Distance from mouse to canvas center
            const deltaX = mouseX - canvasCenterX
            const deltaY = mouseY - canvasCenterY

            // Calculate new offset to keep the mouse point fixed
            const newOffset = {
              x: currentOffset.x - deltaX * (zoomRatio - 1),
              y: currentOffset.y - deltaY * (zoomRatio - 1)
            }

            setZoom(newZoom)
            setCanvasOffset(newOffset)

            // Show zoom indicator briefly
            setShowZoomIndicator(true)
            setTimeout(() => setShowZoomIndicator(false), 1000)
          }
        } else if (event.shiftKey) {
          // Shift + scroll = horizontal pan
          const panSensitivity = 2.0
          const newOffset = {
            x: currentOffset.x - event.deltaY * panSensitivity,
            y: currentOffset.y
          }
          setCanvasOffset(newOffset)
        } else {
          // Regular scroll = vertical pan
          const panSensitivity = 2.0
          const newOffset = {
            x: currentOffset.x,
            y: currentOffset.y - event.deltaY * panSensitivity
          }
          setCanvasOffset(newOffset)
        }
      }
    } catch (error) {
      console.error('Error handling wheel event:', error)
    }
  }, []) // Remove dependencies to prevent stale closures

  // Helper function to calculate distance between two touch points
  const getTouchDistance = useCallback((touch1, touch2) => {
    const dx = touch1.clientX - touch2.clientX
    const dy = touch1.clientY - touch2.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  // Helper function to get center point between two touches
  const getTouchCenter = useCallback((touch1, touch2) => {
    return {
      x: (touch1.clientX + touch2.clientX) / 2,
      y: (touch1.clientY + touch2.clientY) / 2
    }
  }, [])

  // Handle touch start for pinch-to-zoom
  const handleTouchStart = useCallback((event) => {
    if (event.touches.length === 2) {
      event.preventDefault()

      const touch1 = event.touches[0]
      const touch2 = event.touches[1]
      const distance = getTouchDistance(touch1, touch2)
      const center = getTouchCenter(touch1, touch2)

      setTouchState({
        initialDistance: distance,
        initialZoom: zoomRef.current,
        initialCenter: center,
        isGesturing: true
      })
    }
  }, [getTouchDistance, getTouchCenter])

  // Handle touch move for pinch-to-zoom
  const handleTouchMove = useCallback((event) => {
    const currentTouchState = touchStateRef.current
    if (event.touches.length === 2 && currentTouchState.isGesturing) {
      event.preventDefault()

      const touch1 = event.touches[0]
      const touch2 = event.touches[1]
      const currentDistance = getTouchDistance(touch1, touch2)
      const currentCenter = getTouchCenter(touch1, touch2)

      // Calculate zoom based on distance change
      const scale = currentDistance / currentTouchState.initialDistance
      const newZoom = Math.max(0.1, Math.min(15, currentTouchState.initialZoom * scale))

      if (newZoom !== zoomRef.current) {
        const canvas = canvasRef.current
        if (!canvas) return

        const rect = canvas.getBoundingClientRect()
        const zoomRatio = newZoom / zoomRef.current

        // Calculate the point that should remain fixed (touch center relative to canvas)
        const canvasCenterX = rect.width / 2
        const canvasCenterY = rect.height / 2
        const touchCenterX = currentCenter.x - rect.left
        const touchCenterY = currentCenter.y - rect.top

        // Distance from touch center to canvas center
        const deltaX = touchCenterX - canvasCenterX
        const deltaY = touchCenterY - canvasCenterY

        // Calculate new offset to keep the touch center fixed
        const currentOffset = canvasOffsetRef.current
        const newOffset = {
          x: currentOffset.x - deltaX * (zoomRatio - 1),
          y: currentOffset.y - deltaY * (zoomRatio - 1)
        }

        setZoom(newZoom)
        setCanvasOffset(newOffset)

        // Show zoom indicator briefly
        setShowZoomIndicator(true)
        setTimeout(() => setShowZoomIndicator(false), 1000)
      }
    }
  }, [getTouchDistance, getTouchCenter])

  // Handle touch end
  const handleTouchEnd = useCallback((event) => {
    if (event.touches.length < 2) {
      setTouchState(prev => ({ ...prev, isGesturing: false }))
    }
  }, [])

  // Handle Safari/WebKit gesture events for trackpad pinch
  const handleGestureStart = useCallback((event) => {
    console.log('Gesture start detected:', event.scale)
    event.preventDefault()
    setTouchState({
      initialDistance: 1,
      initialZoom: zoomRef.current,
      initialCenter: { x: event.clientX, y: event.clientY },
      isGesturing: true
    })
  }, [])

  const handleGestureChange = useCallback((event) => {
    console.log('Gesture change:', event.scale)
    event.preventDefault()
    const currentTouchState = touchStateRef.current
    if (!currentTouchState.isGesturing) return

    const scale = event.scale
    const newZoom = Math.max(0.1, Math.min(15, currentTouchState.initialZoom * scale))

    if (newZoom !== zoomRef.current) {
      const canvas = canvasRef.current
      if (!canvas) return

      const rect = canvas.getBoundingClientRect()
      const zoomRatio = newZoom / zoomRef.current

      // Calculate the point that should remain fixed (gesture center relative to canvas)
      const canvasCenterX = rect.width / 2
      const canvasCenterY = rect.height / 2
      const gestureCenterX = event.clientX - rect.left
      const gestureCenterY = event.clientY - rect.top

      // Distance from gesture center to canvas center
      const deltaX = gestureCenterX - canvasCenterX
      const deltaY = gestureCenterY - canvasCenterY

      // Calculate new offset to keep the gesture center fixed
      const currentOffset = canvasOffsetRef.current
      const newOffset = {
        x: currentOffset.x - deltaX * (zoomRatio - 1),
        y: currentOffset.y - deltaY * (zoomRatio - 1)
      }

      setZoom(newZoom)
      setCanvasOffset(newOffset)

      // Show zoom indicator briefly
      setShowZoomIndicator(true)
      setTimeout(() => setShowZoomIndicator(false), 1000)
    }
  }, [])

  const handleGestureEnd = useCallback((event) => {
    event.preventDefault()
    setTouchState(prev => ({ ...prev, isGesturing: false }))
  }, [])

  // Handle panning
  const handlePanning = useCallback((event, drawingMode) => {
    if (drawingMode === 'hand') {
      setIsPanning(true)
      setPanStart({ x: event.clientX, y: event.clientY })
      return true
    }
    return false
  }, [])

  const handlePanMove = useCallback((event) => {
    if (isPanning) {
      const deltaX = event.clientX - panStart.x
      const deltaY = event.clientY - panStart.y

      setCanvasOffset(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }))

      setPanStart({ x: event.clientX, y: event.clientY })
      return true
    }
    return false
  }, [isPanning, panStart])

  const handlePanEnd = useCallback(() => {
    setIsPanning(false)
  }, [])

  // Initialize base images when PDF pages change with 100% zoom
  useEffect(() => {
    if (pdfPages.length > 0) {
      // Set default zoom to 100% (1.0) instead of auto-fit
      setZoom(1.0)
      setCanvasOffset({ x: 0, y: 0 })

      // Initialize base images
      pdfPages.forEach((page, index) => {
        if (!baseImages.current[index]) {
          const img = new Image()
          img.onload = () => {
            baseImages.current[index] = img
          }
          img.src = page.imageData
        }
      })
    }
  }, [pdfPages])

  // Add wheel and gesture event listeners
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || pdfPages.length === 0) return

    const wheelHandler = (event) => {
      handleWheel(event)
    }

    // Ensure canvas is ready before adding event listeners
    if (canvas.width > 0 && canvas.height > 0) {
      canvas.addEventListener('wheel', wheelHandler, { passive: false })

      // Add Safari/WebKit gesture event listeners for trackpad pinch
      canvas.addEventListener('gesturestart', handleGestureStart, { passive: false })
      canvas.addEventListener('gesturechange', handleGestureChange, { passive: false })
      canvas.addEventListener('gestureend', handleGestureEnd, { passive: false })
    } else {
      // If canvas isn't ready, wait for next frame
      const rafId = requestAnimationFrame(() => {
        if (canvas.width > 0 && canvas.height > 0) {
          canvas.addEventListener('wheel', wheelHandler, { passive: false })
          canvas.addEventListener('gesturestart', handleGestureStart, { passive: false })
          canvas.addEventListener('gesturechange', handleGestureChange, { passive: false })
          canvas.addEventListener('gestureend', handleGestureEnd, { passive: false })
        }
      })
      return () => {
        cancelAnimationFrame(rafId)
        canvas.removeEventListener('wheel', wheelHandler)
        canvas.removeEventListener('gesturestart', handleGestureStart)
        canvas.removeEventListener('gesturechange', handleGestureChange)
        canvas.removeEventListener('gestureend', handleGestureEnd)
      }
    }

    return () => {
      canvas.removeEventListener('wheel', wheelHandler)
      canvas.removeEventListener('gesturestart', handleGestureStart)
      canvas.removeEventListener('gesturechange', handleGestureChange)
      canvas.removeEventListener('gestureend', handleGestureEnd)
    }
  }, [handleWheel, handleGestureStart, handleGestureChange, handleGestureEnd, pdfPages.length])

  return {
    // State
    zoom,
    canvasOffset,
    isPanning,
    showZoomIndicator,
    canvasRef,
    baseImages,

    // Actions
    setZoom,
    setCanvasOffset,
    screenToCanvasCoordinates,
    autoFitToScreen,
    resetCanvasPosition,
    handlePanning,
    handlePanMove,
    handlePanEnd,

    // Touch handlers
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,

    // Gesture handlers (Safari/WebKit)
    handleGestureStart,
    handleGestureChange,
    handleGestureEnd
  }
}
