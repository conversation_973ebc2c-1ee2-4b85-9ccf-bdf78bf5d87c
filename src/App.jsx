import { useRef, useCallback, useEffect, useState } from 'react'
import { v4 as uuidv4 } from 'uuid'
import './App.css'

// Import components
import {
  usePDFHandler,
  useAnnotationsHandler,
  useCanvasHandler,
  useExportUtils,
  useCSVHandler,
  useHierarchicalFilterState,
  Canvas,
  Toolbar,
  AnnotationsList,
  RoomNameDropdown,
  HierarchicalRoomFilter,
  LandingPage,
  Toast,
  DistanceSortedRoomDropdown
} from './components'
import InteractiveHierarchicalFilter from './components/InteractiveHierarchicalFilter'
import { useRoomCodeSearcher } from './components/RoomCodeSearcher'
import { useDistanceCalculator } from './components/DistanceCalculator'

function App() {
  const fileInputRef = useRef(null)
  const csvInputRef = useRef(null)

  // Use custom hooks for different functionalities
  const {
    pdfFiles,
    currentPdfIndex,
    pdfPages,
    currentPageIndex,
    originalPdfDimensions,
    allPdfData,
    handleFileUpload,
    switchToPdf,
    setCurrentPageIndex,
    canvasToPdfCoordinates,
    pdfToCanvasCoordinates
  } = usePDFHandler()

  const {
    roomNames,
    csvFileName,
    hierarchicalData,
    csvStructure,
    roomCodes,
    handleCSVUpload,
    clearCSVData,
    extractRoomCode
  } = useCSVHandler()

  // Room code searcher - automatically searches for room codes in PDFs
  const {
    searchStatus,
    isSearching,
    searchProgress,
    searchResults,
    getRoomCodeCoordinates,
    getSearchStats,
    getFoundRoomCodes,
    startSearch,
    stopSearch,
    resetSearch,
    createAnnotationAtRoomCode,
    roomCodeCache
  } = useRoomCodeSearcher(allPdfData.current, roomCodes, pdfToCanvasCoordinates, currentPdfIndex)

  // Distance calculator for sorting room names by proximity to annotations
  const {
    sortRoomsByDistance,
    getAnnotationCentroid,
    formatDistance,
    getDistanceIndicator
  } = useDistanceCalculator(roomCodeCache, extractRoomCode)

  const {
    annotations,
    drawingMode,
    currentAnnotation,
    selectedAnnotations,
    isDragging,
    dragOffset,
    copiedAnnotations,
    polygonPoints,
    rectangleStartPoint,
    setDrawingMode,
    setCurrentAnnotation,
    setSelectedAnnotations,
    setIsDragging,
    setDragOffset,
    setCopiedAnnotations,
    setPolygonPoints,
    setRectangleStartPoint,
    getCurrentAnnotations,
    updateCurrentAnnotations,
    findAnnotationAtPoint,
    createRectangleAnnotation,
    finishPolygon,
    copyAnnotation,
    copySelectedAnnotations,
    deleteAnnotation,
    deleteSelectedAnnotations,
    updateAnnotationLabel,
    checkForOverlaps,
    clearDrawingState,
    isAnnotationSelected,
    addToSelection,
    removeFromSelection,
    toggleSelection,
    clearSelection,
    selectSingle,
    getPrimarySelection
  } = useAnnotationsHandler(currentPdfIndex, currentPageIndex)

  const {
    zoom,
    canvasOffset,
    showZoomIndicator,
    canvasRef,
    screenToCanvasCoordinates,
    autoFitToScreen,
    resetCanvasPosition,
    setZoom,
    handlePanning,
    handlePanMove,
    handlePanEnd,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd
  } = useCanvasHandler(pdfPages, currentPageIndex)

  const { exportAnnotations, exportAnnotatedPDF } = useExportUtils(
    allPdfData,
    currentPdfIndex,
    originalPdfDimensions,
    canvasToPdfCoordinates
  )

  // Room name dropdown state
  const [showRoomDropdown, setShowRoomDropdown] = useState(false)
  const [dropdownPosition, setDropdownPosition] = useState({ x: 0, y: 0 })
  const [useDistanceSorting, setUseDistanceSorting] = useState(true) // Toggle for distance-based sorting
  const [pendingAnnotation, setPendingAnnotation] = useState(null)
  const [showHierarchicalFilter, setShowHierarchicalFilter] = useState(false)
  const [showPreFilter, setShowPreFilter] = useState(false)
  const [useInteractiveFilter, setUseInteractiveFilter] = useState(true)
  const [isProcessingPDF, setIsProcessingPDF] = useState(false)

  // Hierarchical filter state
  const hierarchicalFilter = useHierarchicalFilterState(csvStructure, hierarchicalData)

  // Wrapper for file upload with loading state
  const handleFileUploadWithLoading = useCallback(async (event) => {
    const files = Array.from(event.target.files)
    if (files.length === 0) return

    setIsProcessingPDF(true)
    try {
      await handleFileUpload(event)
    } finally {
      setIsProcessingPDF(false)
    }
  }, [handleFileUpload])

  // Pre-filter popup disabled - users can access filter via toolbar
  // useEffect(() => {
  //   if (pdfPages.length > 0 && csvStructure && csvStructure.maxDepth > 1 && !hierarchicalFilter.isFilterActive) {
  //     setShowPreFilter(true)
  //   } else {
  //     setShowPreFilter(false)
  //   }
  // }, [pdfPages.length, csvStructure, hierarchicalFilter.isFilterActive])

  // Handle drawing mode changes
  const handleDrawingModeChange = useCallback((mode) => {
    setDrawingMode(mode)
    clearDrawingState()
  }, [setDrawingMode, clearDrawingState])

  // Wrapper functions for toolbar actions
  const handleZoomChange = useCallback((newZoom) => {
    setZoom(newZoom)
  }, [setZoom])

  const handleAutoFit = useCallback(() => {
    autoFitToScreen()
  }, [autoFitToScreen])

  const handleResetPosition = useCallback(() => {
    resetCanvasPosition()
  }, [resetCanvasPosition])

  // Room name dropdown handlers
  const handleRoomSelection = useCallback((roomName, roomPath = null) => {
    if (pendingAnnotation) {
      // Update the annotation with the selected room name and path
      const updatedAnnotation = {
        ...pendingAnnotation,
        label: roomName,
        roomName: roomName,
        roomPath: roomPath // Store hierarchical path if available
      }

      // Add the annotation to the current annotations
      const currentAnnotations = getCurrentAnnotations()
      updateCurrentAnnotations([...currentAnnotations, updatedAnnotation])

      // Select the newly created annotation to highlight it
      selectSingle(updatedAnnotation)

      // Clear pending state
      setPendingAnnotation(null)
      setShowRoomDropdown(false)
      setShowHierarchicalFilter(false)
    }
  }, [pendingAnnotation, getCurrentAnnotations, updateCurrentAnnotations])

  const handleRoomDropdownCancel = useCallback(() => {
    setPendingAnnotation(null)
    setShowRoomDropdown(false)
    setShowHierarchicalFilter(false)
  }, [])

  // Pre-filter handlers
  const handlePreFilterApply = useCallback((filterData) => {
    hierarchicalFilter.updateFilterSelection(filterData.selectedPath)
    setShowPreFilter(false)
  }, [hierarchicalFilter])

  const handlePreFilterSkip = useCallback(() => {
    setShowPreFilter(false)
  }, [])

  // Calculate smart dropdown position that stays within viewport bounds
  const calculateDropdownPosition = useCallback((mouseX, mouseY) => {
    const dropdownWidth = 450 // Max width of dropdown (from CSS)
    const dropdownHeight = 400 // Max height of dropdown (from CSS)
    const padding = 20 // Minimum distance from viewport edges

    let x = mouseX + 10 // Default offset from mouse
    let y = mouseY + 10

    // Adjust X position if dropdown would go off right edge
    if (x + dropdownWidth > window.innerWidth - padding) {
      x = mouseX - dropdownWidth - 10 // Position to the left of mouse
      // If still off screen, clamp to right edge
      if (x < padding) {
        x = window.innerWidth - dropdownWidth - padding
      }
    }

    // Ensure X is not off left edge
    if (x < padding) {
      x = padding
    }

    // Final clamp for X to ensure it doesn't exceed right boundary
    if (x + dropdownWidth > window.innerWidth - padding) {
      x = window.innerWidth - dropdownWidth - padding
    }

    // Adjust Y position if dropdown would go off bottom edge
    if (y + dropdownHeight > window.innerHeight - padding) {
      y = mouseY - dropdownHeight - 10 // Position above mouse
      // If still off screen, clamp to bottom edge
      if (y < padding) {
        y = window.innerHeight - dropdownHeight - padding
      }
    }

    // Ensure Y is not off top edge
    if (y < padding) {
      y = padding
    }

    // Final clamp for Y to ensure it doesn't exceed bottom boundary
    if (y + dropdownHeight > window.innerHeight - padding) {
      y = window.innerHeight - dropdownHeight - padding
    }

    return { x, y }
  }, [])

  // Handler to show filter modal from toolbar
  const handleShowFilterModal = useCallback(() => {
    setShowHierarchicalFilter(true)
    // Use smart positioning for filter modal, centered but within bounds
    const centerX = window.innerWidth / 2
    const centerY = window.innerHeight / 2
    const smartPosition = calculateDropdownPosition(centerX, centerY)
    setDropdownPosition(smartPosition)
  }, [calculateDropdownPosition])

  // Show room dropdown after annotation creation
  const showRoomDropdownForAnnotation = useCallback((annotation, mouseEvent) => {
    if (roomNames.length === 0) {
      // No CSV loaded, proceed with default annotation creation
      const currentAnnotations = getCurrentAnnotations()
      updateCurrentAnnotations([...currentAnnotations, annotation])
      return
    }

    // Set up dropdown with smart positioning
    setPendingAnnotation(annotation)
    const smartPosition = calculateDropdownPosition(mouseEvent.clientX, mouseEvent.clientY)
    setDropdownPosition(smartPosition)

    // Determine if we should use distance sorting
    // Use distance sorting if we have room code coordinates and the feature is enabled
    const shouldUseDistanceSorting = useDistanceSorting &&
                                   roomCodeCache &&
                                   roomCodeCache.size > 0 &&
                                   extractRoomCode

    if (shouldUseDistanceSorting) {
      // Use distance-sorted dropdown
      setShowRoomDropdown(true)
      setShowHierarchicalFilter(false)
    } else {
      // Use regular dropdown
      setShowRoomDropdown(true)
      setShowHierarchicalFilter(false)
    }
  }, [roomNames.length, getCurrentAnnotations, updateCurrentAnnotations, useDistanceSorting, roomCodeCache, extractRoomCode, calculateDropdownPosition])

  // Mouse event handlers
  const handleMouseDown = useCallback((event) => {
    // Handle panning first
    const panHandled = handlePanning(event, drawingMode)
    if (panHandled) return

    const canvasCoords = screenToCanvasCoordinates(event.clientX, event.clientY)

    // Check if clicking on existing annotation for selection/dragging
    const clickedAnnotation = findAnnotationAtPoint(canvasCoords)

    if (clickedAnnotation && (drawingMode === 'select' || drawingMode !== 'hand')) {
      // Handle multi-selection with Ctrl+click
      if (event.ctrlKey || event.metaKey) {
        // Ctrl+click: toggle selection
        toggleSelection(clickedAnnotation)
      } else {
        // Regular click: single selection
        if (!isAnnotationSelected(clickedAnnotation)) {
          selectSingle(clickedAnnotation)
        }
      }

      if (drawingMode === 'select') {
        setIsDragging(true)

        if (clickedAnnotation.type === 'rectangle') {
          setDragOffset({
            x: canvasCoords.x - clickedAnnotation.x,
            y: canvasCoords.y - clickedAnnotation.y
          })
        } else if (clickedAnnotation.type === 'polygon') {
          // For polygons, use the first point as reference
          setDragOffset({
            x: canvasCoords.x - clickedAnnotation.points[0].x,
            y: canvasCoords.y - clickedAnnotation.points[0].y
          })
        }
      }
      return
    }

    // Handle drawing based on mode
    if (drawingMode === 'rectangle') {
      // Start dragging to create rectangle
      setRectangleStartPoint(canvasCoords)
      setIsDragging(true)
      clearSelection()
    }
  }, [drawingMode, handlePanning, screenToCanvasCoordinates, findAnnotationAtPoint, rectangleStartPoint, createRectangleAnnotation, clearSelection, setIsDragging, setDragOffset, setRectangleStartPoint, showRoomDropdownForAnnotation, roomNames.length, toggleSelection, isAnnotationSelected, selectSingle])

  const handleMouseMove = useCallback((event) => {
    // Handle panning first
    const panHandled = handlePanMove(event)
    if (panHandled) return

    const canvasCoords = screenToCanvasCoordinates(event.clientX, event.clientY)

    // Handle dragging selected annotations (only in select mode)
    if (isDragging && selectedAnnotations.length > 0 && drawingMode === 'select') {
      const primarySelection = getPrimarySelection()
      if (!primarySelection) return

      const newX = canvasCoords.x - dragOffset.x
      const newY = canvasCoords.y - dragOffset.y

      // Calculate the delta from the primary selection's current position
      let deltaX = 0, deltaY = 0
      if (primarySelection.type === 'rectangle') {
        deltaX = newX - primarySelection.x
        deltaY = newY - primarySelection.y
      } else if (primarySelection.type === 'polygon') {
        deltaX = newX - primarySelection.points[0].x
        deltaY = newY - primarySelection.points[0].y
      }

      const selectedIds = new Set(selectedAnnotations.map(ann => ann.id))
      const currentAnnotations = getCurrentAnnotations()

      const updatedAnnotations = currentAnnotations.map(annotation => {
        if (selectedIds.has(annotation.id)) {
          if (annotation.type === 'rectangle') {
            return {
              ...annotation,
              x: annotation.x + deltaX,
              y: annotation.y + deltaY
            }
          } else if (annotation.type === 'polygon') {
            return {
              ...annotation,
              points: annotation.points.map(point => ({
                x: point.x + deltaX,
                y: point.y + deltaY
              }))
            }
          }
        }
        return annotation
      })

      updateCurrentAnnotations(updatedAnnotations)

      // Update selected annotations with new positions
      setSelectedAnnotations(prev => prev.map(selected => {
        if (selected.type === 'rectangle') {
          return {
            ...selected,
            x: selected.x + deltaX,
            y: selected.y + deltaY
          }
        } else if (selected.type === 'polygon') {
          return {
            ...selected,
            points: selected.points.map(point => ({
              x: point.x + deltaX,
              y: point.y + deltaY
            }))
          }
        }
        return selected
      }))
    }
  }, [handlePanMove, isDragging, selectedAnnotations, drawingMode, dragOffset, screenToCanvasCoordinates, getCurrentAnnotations, updateCurrentAnnotations, setSelectedAnnotations, getPrimarySelection])

  const handleMouseUp = useCallback((event) => {
    handlePanEnd()

    // Handle rectangle creation on drag end
    if (drawingMode === 'rectangle' && rectangleStartPoint && isDragging) {
      const canvasCoords = screenToCanvasCoordinates(event.clientX, event.clientY)

      // Create rectangle from start point to current point
      if (roomNames.length > 0) {
        // CSV loaded, use room dropdown
        createRectangleAnnotation(rectangleStartPoint, canvasCoords, (annotation) => {
          showRoomDropdownForAnnotation(annotation, event)
        })
      } else {
        // No CSV loaded, create rectangle normally
        const newAnnotation = createRectangleAnnotation(rectangleStartPoint, canvasCoords)
        if (newAnnotation) {
          selectSingle(newAnnotation)
        }
      }
      setRectangleStartPoint(null)
    }

    setIsDragging(false)
  }, [handlePanEnd, setIsDragging, drawingMode, rectangleStartPoint, isDragging, screenToCanvasCoordinates, roomNames.length, createRectangleAnnotation, showRoomDropdownForAnnotation, setRectangleStartPoint])

  const handleCanvasClick = useCallback((event) => {
    // Prevent default click behavior when dragging or panning
    if (isDragging) return

    // Hand mode and select mode don't handle clicks for annotation creation
    if (drawingMode === 'hand' || drawingMode === 'select') return

    const canvasCoords = screenToCanvasCoordinates(event.clientX, event.clientY)

    if (drawingMode === 'polygon') {
      // Check if clicking on existing annotation first
      const clickedAnnotation = findAnnotationAtPoint(canvasCoords)
      if (clickedAnnotation) {
        selectSingle(clickedAnnotation)
        return
      }

      if (!currentAnnotation) {
        // Start new polygon (store in canvas coordinates)
        const currentAnnotations = getCurrentAnnotations()
        const annotationNumber = currentAnnotations.length + 1
        setCurrentAnnotation({
          id: uuidv4(),
          type: 'polygon',
          pageIndex: currentPageIndex,
          points: [canvasCoords],
          color: '#ff0000',
          label: `Polygon ${annotationNumber}`
        })
        setPolygonPoints([canvasCoords])
        clearSelection()
      } else {
        // Add point to current polygon
        const newPoints = [...polygonPoints, canvasCoords]
        setPolygonPoints(newPoints)
        setCurrentAnnotation(prev => ({
          ...prev,
          points: newPoints
        }))
      }
    } else if (drawingMode === 'rectangle') {
      // For rectangle mode, clear selection if clicking on empty space
      const clickedAnnotation = findAnnotationAtPoint(canvasCoords)
      if (!clickedAnnotation && !rectangleStartPoint) {
        clearSelection()
      }
    }
  }, [drawingMode, screenToCanvasCoordinates, currentAnnotation, polygonPoints, currentPageIndex, isDragging, findAnnotationAtPoint, rectangleStartPoint, setCurrentAnnotation, setPolygonPoints, selectSingle, clearSelection])

  // Global mouse event listeners to handle mouse release outside window
  useEffect(() => {
    const handleGlobalMouseUp = () => {
      // Clean up any active panning or dragging operations
      handlePanEnd()
      setIsDragging(false)
    }

    const handleGlobalMouseLeave = (event) => {
      // If mouse leaves the entire document, stop any active operations
      if (event.target === document.documentElement) {
        handlePanEnd()
        setIsDragging(false)
      }
    }

    // Add global listeners
    document.addEventListener('mouseup', handleGlobalMouseUp)
    document.addEventListener('mouseleave', handleGlobalMouseLeave)

    return () => {
      document.removeEventListener('mouseup', handleGlobalMouseUp)
      document.removeEventListener('mouseleave', handleGlobalMouseLeave)
    }
  }, [handlePanEnd, setIsDragging])

  // Keyboard shortcuts handler
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Only handle shortcuts if not typing in an input field or editing annotation labels
      const activeElement = document.activeElement
      if (event.target.tagName === 'INPUT' ||
          event.target.tagName === 'TEXTAREA' ||
          event.target.classList.contains('annotation-label-input') ||
          event.target.dataset.editingLabel === 'true' ||
          event.target.contentEditable === 'true' ||
          (activeElement && activeElement.tagName === 'INPUT') ||
          (activeElement && activeElement.classList.contains('annotation-label-input'))) {
        return
      }

      if (event.ctrlKey && event.key === 's') {
        event.preventDefault()
        exportAnnotations(annotations)
      } else if (event.ctrlKey && event.key === 'c') {
        event.preventDefault()
        if (selectedAnnotations.length > 0) {
          copySelectedAnnotations()
        }
      } else if (event.ctrlKey && event.key === 'v') {
        event.preventDefault()
        if (copiedAnnotations.length > 0) {
          const currentAnnotations = getCurrentAnnotations()
          const newAnnotations = copiedAnnotations.map((copiedAnnotation, index) => {
            const annotationNumber = currentAnnotations.length + index + 1
            const newAnnotation = {
              ...copiedAnnotation,
              id: uuidv4(),
              pageIndex: currentPageIndex,
              label: copiedAnnotation.label ? `${copiedAnnotation.label} (Copy)` : `${copiedAnnotation.type} ${annotationNumber}`
            }

            // Offset the pasted annotation slightly
            if (newAnnotation.type === 'rectangle') {
              newAnnotation.x += 20
              newAnnotation.y += 20
            } else if (newAnnotation.type === 'polygon') {
              newAnnotation.points = newAnnotation.points.map(point => ({
                x: point.x + 20,
                y: point.y + 20
              }))
            }

            return newAnnotation
          })

          // No overlap check for paste operations since they're intentionally offset from the original
          updateCurrentAnnotations([...getCurrentAnnotations(), ...newAnnotations])
          setSelectedAnnotations(newAnnotations)
        }
      } else if (event.key === 'Delete' || event.key === 'Backspace') {
        event.preventDefault()
        if (selectedAnnotations.length > 0) {
          deleteSelectedAnnotations()
        }
      } else if (event.key === 'Enter') {
        event.preventDefault()
        if (currentAnnotation && drawingMode === 'polygon' && polygonPoints.length >= 3) {
          if (roomNames.length > 0) {
            // Use room dropdown for polygon
            finishPolygon((annotation) => {
              // Create a synthetic mouse event for positioning
              const syntheticEvent = {
                clientX: window.innerWidth / 2,
                clientY: window.innerHeight / 2
              }
              showRoomDropdownForAnnotation(annotation, syntheticEvent)
            })
          } else {
            // No CSV loaded, finish normally
            const finished = finishPolygon()
            if (finished && currentAnnotation) {
              selectSingle(currentAnnotation)
            }
          }
        }
      } else if (event.key === 'h' || event.key === 'H') {
        handleDrawingModeChange('hand')
      } else if (event.key === 'r' || event.key === 'R') {
        handleDrawingModeChange('rectangle')
      } else if (event.key === 'p' || event.key === 'P') {
        handleDrawingModeChange('polygon')
      } else if (event.key === 's' || event.key === 'S') {
        handleDrawingModeChange('select')
      } else if (event.key === 'Escape') {
        clearDrawingState()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [exportAnnotations, annotations, selectedAnnotations, copiedAnnotations, currentPageIndex, currentAnnotation, drawingMode, finishPolygon, polygonPoints.length, copySelectedAnnotations, updateCurrentAnnotations, getCurrentAnnotations, setSelectedAnnotations, deleteSelectedAnnotations, handleDrawingModeChange, clearDrawingState, roomNames.length, showRoomDropdownForAnnotation, selectSingle])

  return (
    <div className="app">
      {/* Show landing page when no PDFs are loaded */}
      {allPdfData.current.length === 0 ? (
        <LandingPage
          fileInputRef={fileInputRef}
          onFileUpload={handleFileUploadWithLoading}
          csvInputRef={csvInputRef}
          onCSVUpload={handleCSVUpload}
          csvFileName={csvFileName}
          roomNames={roomNames}
        />
      ) : (
        <>
          <Toolbar
        fileInputRef={fileInputRef}
        onFileUpload={handleFileUploadWithLoading}
        csvInputRef={csvInputRef}
        onCSVUpload={handleCSVUpload}
        csvFileName={csvFileName}
        roomNames={roomNames}
        allPdfData={allPdfData}
        currentPdfIndex={currentPdfIndex}
        onSwitchToPdf={switchToPdf}
        pdfPages={pdfPages}
        currentPageIndex={currentPageIndex}
        onSetCurrentPageIndex={setCurrentPageIndex}
        zoom={zoom}
        onSetZoom={handleZoomChange}
        onAutoFitToScreen={handleAutoFit}
        onResetCanvasPosition={handleResetPosition}
        drawingMode={drawingMode}
        onSetDrawingMode={handleDrawingModeChange}
        currentAnnotation={currentAnnotation}
        onFinishPolygon={(onAnnotationCreated) => finishPolygon(onAnnotationCreated)}
        rectangleStartPoint={rectangleStartPoint}
        onSetRectangleStartPoint={setRectangleStartPoint}
        onExportAnnotations={() => exportAnnotations(annotations)}
        onExportAnnotatedPDF={() => exportAnnotatedPDF(annotations)}
        getCurrentAnnotations={getCurrentAnnotations}
        selectedAnnotations={selectedAnnotations}
        onCopyAnnotation={copySelectedAnnotations}
        onDeleteAnnotation={deleteSelectedAnnotations}
        showRoomDropdownForAnnotation={showRoomDropdownForAnnotation}
        csvStructure={csvStructure}
        hierarchicalFilter={hierarchicalFilter}
        onShowFilterModal={handleShowFilterModal}
        useDistanceSorting={useDistanceSorting}
        onToggleDistanceSorting={() => setUseDistanceSorting(!useDistanceSorting)}
        roomCodeCache={roomCodeCache}
      />



      <div className="main-content">
        <Canvas
          pdfPages={pdfPages}
          currentPageIndex={currentPageIndex}
          annotations={getCurrentAnnotations()}
          selectedAnnotations={selectedAnnotations}
          currentAnnotation={currentAnnotation}
          polygonPoints={polygonPoints}
          rectangleStartPoint={rectangleStartPoint}
          drawingMode={drawingMode}
          zoom={zoom}
          canvasOffset={canvasOffset}
          showZoomIndicator={showZoomIndicator}
          canvasRef={canvasRef}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onClick={handleCanvasClick}
          onContextMenu={(e) => {
            e.preventDefault()
            // Handle right-click context menu if needed
          }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          isDragging={isDragging}
        />

        {getCurrentAnnotations().length > 0 && (
          <AnnotationsList
            annotations={getCurrentAnnotations()}
            selectedAnnotations={selectedAnnotations}
            onSelectAnnotation={selectSingle}
            onCopyAnnotation={copyAnnotation}
            onDeleteAnnotation={deleteAnnotation}
            onUpdateAnnotationLabel={updateAnnotationLabel}
            onCheckForOverlaps={checkForOverlaps}
          />
        )}
      </div>

      {/* Conditional room dropdown - use distance-sorted if room codes are available */}
      {useDistanceSorting && roomCodeCache && roomCodeCache.size > 0 && extractRoomCode ? (
        <DistanceSortedRoomDropdown
          roomNames={hierarchicalFilter.isFilterActive ? hierarchicalFilter.getFilteredRoomNames() : roomNames}
          position={dropdownPosition}
          onSelectRoom={handleRoomSelection}
          onCancel={handleRoomDropdownCancel}
          isVisible={showRoomDropdown}
          currentAnnotations={getCurrentAnnotations()}
          annotation={pendingAnnotation}
          roomCodeCache={roomCodeCache}
          extractRoomCode={extractRoomCode}
        />
      ) : (
        <RoomNameDropdown
          roomNames={hierarchicalFilter.isFilterActive ? hierarchicalFilter.getFilteredRoomNames() : roomNames}
          position={dropdownPosition}
          onSelectRoom={handleRoomSelection}
          onCancel={handleRoomDropdownCancel}
          isVisible={showRoomDropdown}
          currentAnnotations={getCurrentAnnotations()}
        />
      )}

      {showHierarchicalFilter && (
        <div
          className="hierarchical-filter-overlay"
          style={{
            position: 'fixed',
            left: dropdownPosition.x,
            top: dropdownPosition.y,
            zIndex: 9999
          }}
        >
          <InteractiveHierarchicalFilter
            csvStructure={csvStructure}
            hierarchicalData={hierarchicalData}
            onRoomSelection={pendingAnnotation ? handleRoomSelection : null}
            onFilterChange={(filterData) => {
              hierarchicalFilter.updateFilterSelection(filterData.selectedPath)
              if (!pendingAnnotation && filterData.applied) {
                // If opened from toolbar and filter is applied, close modal
                setShowHierarchicalFilter(false)
              }
            }}
            onClose={() => setShowHierarchicalFilter(false)}
            className={pendingAnnotation ? "room-selection-filter" : "toolbar-filter"}
          />
        </div>
      )}

      {showPreFilter && (
        <div className="pre-filter-overlay">
          <div className="pre-filter-modal">
            <div className="pre-filter-header">
              <h3>🎯 Select Your Working Area</h3>
              <p>Choose your working area to filter room suggestions while drawing annotations.</p>
            </div>
            <HierarchicalRoomFilter
              csvStructure={csvStructure}
              hierarchicalData={hierarchicalData}
              onRoomSelection={(roomName, roomPath) => {
                // For pre-filter, we don't select a specific room, just apply the path filter
                const pathWithoutRoom = roomPath ? roomPath.slice(0, -1) : []
                handlePreFilterApply({ selectedPath: pathWithoutRoom })
              }}
              onFilterChange={handlePreFilterApply}
              className="pre-filter"
            />
            <div className="pre-filter-actions">
              <button
                className="skip-button"
                onClick={handlePreFilterSkip}
              >
                Skip - Show All Rooms
              </button>
              {hierarchicalFilter.isFilterActive && (
                <button
                  className="apply-button"
                  onClick={() => setShowPreFilter(false)}
                >
                  Apply Filter
                </button>
              )}
            </div>
          </div>
        </div>
      )}
        </>
      )}

      {/* Loading Toast for PDF Processing */}
      <Toast
        message="Processing PDF files... This may take a moment."
        type="loading"
        isVisible={isProcessingPDF}
      />
    </div>
  )
}

export default App
